-- Notification Queue Management Functions
-- Functions for managing the notification queue with TTL support

-- Function to queue a notification for a single device
CREATE OR REPLACE FUNCTION queue_notification_for_device(
  p_user_id UUID,
  p_target_device_id UUID,
  p_title TEXT,
  p_body TEXT,
  p_data JSONB DEFAULT '{}',
  p_channel_type TEXT DEFAULT 'other',
  p_ttl_minutes INTEGER DEFAULT 60,
  p_priority INTEGER DEFAULT 0,
  p_scheduled_for TIMESTAMPTZ DEFAULT NOW()
)
RETURNS UUID AS $$
DECLARE
  notification_id UUID;
  expires_at TIMESTAMPTZ;
BEGIN
  -- Calculate expiration time
  expires_at := p_scheduled_for + (p_ttl_minutes || ' minutes')::INTERVAL;

  -- Validate that the target device belongs to the user
  IF NOT EXISTS (
    SELECT 1 FROM notification_targets
    WHERE id = p_target_device_id
    AND user_id = p_user_id
    AND is_active = true
  ) THEN
    RAISE EXCEPTION 'Invalid target device for user';
  END IF;

  -- Insert notification into queue
  INSERT INTO notification_queue (
    user_id,
    target_device_id,
    title,
    body,
    data,
    channel_type,
    priority,
    expires_at,
    scheduled_for
  ) VALUES (
    p_user_id,
    p_target_device_id,
    p_title,
    p_body,
    p_data,
    p_channel_type,
    p_priority,
    expires_at,
    p_scheduled_for
  ) RETURNING id INTO notification_id;

  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to queue a notification for all devices of a user
CREATE OR REPLACE FUNCTION queue_notification_for_user(
  p_user_id UUID,
  p_title TEXT,
  p_body TEXT,
  p_data JSONB DEFAULT '{}',
  p_channel_type TEXT DEFAULT 'other',
  p_ttl_minutes INTEGER DEFAULT 60,
  p_priority INTEGER DEFAULT 0,
  p_scheduled_for TIMESTAMPTZ DEFAULT NOW()
)
RETURNS UUID[] AS $$
DECLARE
  notification_ids UUID[] := '{}';
  device_record RECORD;
  notification_id UUID;
  expires_at TIMESTAMPTZ;
BEGIN
  -- Calculate expiration time
  expires_at := p_scheduled_for + (p_ttl_minutes || ' minutes')::INTERVAL;

  -- Get all active devices for the user that have the channel enabled
  FOR device_record IN
    SELECT id
    FROM notification_targets
    WHERE user_id = p_user_id
    AND is_active = true
    AND (notification_preferences->p_channel_type)::boolean = true
  LOOP
    -- Insert notification for each device
    INSERT INTO notification_queue (
      user_id,
      target_device_id,
      title,
      body,
      data,
      channel_type,
      priority,
      expires_at,
      scheduled_for
    ) VALUES (
      p_user_id,
      device_record.id,
      p_title,
      p_body,
      p_data,
      p_channel_type,
      p_priority,
      expires_at,
      p_scheduled_for
    ) RETURNING id INTO notification_id;

    notification_ids := array_append(notification_ids, notification_id);
  END LOOP;

  RETURN notification_ids;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get pending notifications ready to be sent
CREATE OR REPLACE FUNCTION get_pending_notifications(
  p_limit INTEGER DEFAULT 100
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  target_device_id UUID,
  push_token TEXT,
  device_type TEXT,
  title TEXT,
  body TEXT,
  data JSONB,
  channel_type TEXT,
  priority INTEGER,
  attempt_count INTEGER,
  max_attempts INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    nq.id,
    nq.user_id,
    nq.target_device_id,
    nt.push_token,
    nt.device_type,
    nq.title,
    nq.body,
    nq.data,
    nq.channel_type,
    nq.priority,
    nq.attempt_count,
    nq.max_attempts
  FROM notification_queue nq
  JOIN notification_targets nt ON nq.target_device_id = nt.id
  WHERE nq.status = 'pending'
    AND nq.scheduled_for <= NOW()
    AND nq.expires_at > NOW()
    AND nt.is_active = true
  ORDER BY nq.priority DESC, nq.created_at ASC
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark notification as being processed
CREATE OR REPLACE FUNCTION mark_notification_processing(
  p_notification_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE notification_queue
  SET
    status = 'processing',
    last_attempt_at = NOW(),
    attempt_count = attempt_count + 1
  WHERE id = p_notification_id
    AND status = 'pending'
    AND expires_at > NOW();

  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark notification as successfully sent (removes from queue)
CREATE OR REPLACE FUNCTION mark_notification_sent(
  p_notification_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Update the last_notification_at timestamp on the target device
  UPDATE notification_targets
  SET last_notification_at = NOW()
  WHERE id = (
    SELECT target_device_id
    FROM notification_queue
    WHERE id = p_notification_id
  );

  -- Remove the notification from the queue
  DELETE FROM notification_queue
  WHERE id = p_notification_id;

  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark notification as failed
CREATE OR REPLACE FUNCTION mark_notification_failed(
  p_notification_id UUID,
  p_error_message TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  current_attempts INTEGER;
  max_attempts INTEGER;
BEGIN
  -- Get current attempt count and max attempts
  SELECT attempt_count, max_attempts
  INTO current_attempts, max_attempts
  FROM notification_queue
  WHERE id = p_notification_id;

  -- If we've reached max attempts, mark as failed and remove
  IF current_attempts >= max_attempts THEN
    DELETE FROM notification_queue
    WHERE id = p_notification_id;
  ELSE
    -- Reset to pending for retry
    UPDATE notification_queue
    SET
      status = 'pending',
      error_message = p_error_message
    WHERE id = p_notification_id;
  END IF;

  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup expired notifications
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete expired notifications
  DELETE FROM notification_queue
  WHERE expires_at <= NOW();

  GET DIAGNOSTICS deleted_count = ROW_COUNT;

  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get queue statistics
CREATE OR REPLACE FUNCTION get_queue_statistics()
RETURNS TABLE (
  total_pending INTEGER,
  total_processing INTEGER,
  total_expired INTEGER,
  oldest_pending TIMESTAMPTZ,
  newest_pending TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*) FILTER (WHERE status = 'pending')::INTEGER as total_pending,
    COUNT(*) FILTER (WHERE status = 'processing')::INTEGER as total_processing,
    COUNT(*) FILTER (WHERE expires_at <= NOW())::INTEGER as total_expired,
    MIN(created_at) FILTER (WHERE status = 'pending') as oldest_pending,
    MAX(created_at) FILTER (WHERE status = 'pending') as newest_pending
  FROM notification_queue;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users and service role
GRANT EXECUTE ON FUNCTION queue_notification_for_device TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION queue_notification_for_user TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION get_pending_notifications TO service_role;
GRANT EXECUTE ON FUNCTION mark_notification_processing TO service_role;
GRANT EXECUTE ON FUNCTION mark_notification_sent TO service_role;
GRANT EXECUTE ON FUNCTION mark_notification_failed TO service_role;
GRANT EXECUTE ON FUNCTION cleanup_expired_notifications TO service_role;
GRANT EXECUTE ON FUNCTION get_queue_statistics TO authenticated, service_role;
