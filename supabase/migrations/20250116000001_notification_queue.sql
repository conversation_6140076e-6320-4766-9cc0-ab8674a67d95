-- Notification Queue System Migration
-- Creates tables and functions for queuing notifications with TTL support

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create notification_targets table if it doesn't exist
-- This stores device registration information
CREATE TABLE IF NOT EXISTS notification_targets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  push_token TEXT NOT NULL,
  device_type TEXT NOT NULL CHECK (device_type IN ('ios', 'android', 'web')),
  device_info JSONB DEFAULT '{}',
  notification_preferences JSONB DEFAULT '{"questions": true, "recommendations": true, "other": true}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  last_notification_at TIMESTAMPTZ,
  
  -- Ensure unique active tokens per user
  UNIQUE(user_id, push_token)
);

-- Create notification_queue table for queuing notifications
CREATE TABLE notification_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  target_device_id UUID REFERENCES notification_targets(id) ON DELETE CASCADE, -- NULL means all devices for user
  
  -- Notification content
  title TEXT NOT NULL,
  body TEXT NOT NULL,
  data JSONB DEFAULT '{}',
  channel_type TEXT NOT NULL DEFAULT 'other' CHECK (channel_type IN ('questions', 'recommendations', 'other')),
  
  -- Queue management
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'sent', 'failed', 'expired')),
  priority INTEGER DEFAULT 0, -- Higher number = higher priority
  max_attempts INTEGER DEFAULT 3,
  attempt_count INTEGER DEFAULT 0,
  
  -- TTL and timing
  expires_at TIMESTAMPTZ NOT NULL,
  scheduled_for TIMESTAMPTZ DEFAULT NOW(), -- When to send (for delayed notifications)
  last_attempt_at TIMESTAMPTZ,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  error_message TEXT
);

-- Create indexes for performance
CREATE INDEX idx_notification_targets_user_id ON notification_targets(user_id);
CREATE INDEX idx_notification_targets_push_token ON notification_targets(push_token);
CREATE INDEX idx_notification_targets_active ON notification_targets(user_id, is_active) WHERE is_active = true;

CREATE INDEX idx_notification_queue_user_id ON notification_queue(user_id);
CREATE INDEX idx_notification_queue_status ON notification_queue(status);
CREATE INDEX idx_notification_queue_expires_at ON notification_queue(expires_at);
CREATE INDEX idx_notification_queue_scheduled_for ON notification_queue(scheduled_for);
CREATE INDEX idx_notification_queue_priority ON notification_queue(priority DESC, created_at ASC);
CREATE INDEX idx_notification_queue_pending ON notification_queue(status, scheduled_for, expires_at) 
  WHERE status = 'pending';

-- Enable RLS
ALTER TABLE notification_targets ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_queue ENABLE ROW LEVEL SECURITY;

-- RLS Policies for notification_targets
CREATE POLICY "Users can view their own notification targets"
  ON notification_targets FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own notification targets"
  ON notification_targets FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own notification targets"
  ON notification_targets FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own notification targets"
  ON notification_targets FOR DELETE
  USING (auth.uid() = user_id);

-- RLS Policies for notification_queue
CREATE POLICY "Users can view their own queued notifications"
  ON notification_queue FOR SELECT
  USING (auth.uid() = user_id);

-- Service role can manage all queue operations
CREATE POLICY "Service role can manage notification queue"
  ON notification_queue FOR ALL
  USING (auth.role() = 'service_role');

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
CREATE TRIGGER update_notification_targets_updated_at 
  BEFORE UPDATE ON notification_targets 
  FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_notification_queue_updated_at 
  BEFORE UPDATE ON notification_queue 
  FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
