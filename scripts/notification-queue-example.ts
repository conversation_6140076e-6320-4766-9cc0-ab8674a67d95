/**
 * Example script demonstrating the Notification Queue System
 * This shows how to queue notifications and process them
 */

import { notificationQueueService } from '../app/services/NotificationQueueService'

// Example: Queue a notification for a specific device
async function queueDeviceNotification() {
  console.log('📱 Queuing notification for specific device...')
  
  const result = await notificationQueueService.queueNotificationForDevice(
    'user-123', // User ID
    'device-456', // Target device ID
    {
      title: 'Profile Question',
      body: 'Please complete your profile to get better recommendations',
      channelType: 'questions',
      ttlMinutes: 60, // Expire after 1 hour
      priority: 1, // Higher priority
      data: {
        questionId: 'q123',
        action: 'complete_profile',
        deepLink: '/profile/questions'
      }
    }
  )
  
  if (result.success) {
    console.log('✅ Notification queued successfully:', result.data)
  } else {
    console.error('❌ Failed to queue notification:', result.error)
  }
}

// Example: Queue a notification for all user devices
async function queueUserNotification() {
  console.log('👥 Queuing notification for all user devices...')
  
  const result = await notificationQueueService.queueNotificationForUser(
    'user-123', // User ID
    {
      title: 'New Recommendation',
      body: 'We found a great product recommendation for you!',
      channelType: 'recommendations',
      ttlMinutes: 120, // Expire after 2 hours
      priority: 0, // Normal priority
      data: {
        productId: 'prod-789',
        category: 'electronics',
        deepLink: '/recommendations/prod-789'
      }
    }
  )
  
  if (result.success) {
    console.log('✅ Notifications queued for devices:', result.data?.length)
  } else {
    console.error('❌ Failed to queue notifications:', result.error)
  }
}

// Example: Process pending notifications (background job)
async function processPendingNotifications() {
  console.log('⚙️ Processing pending notifications...')
  
  const pending = await notificationQueueService.getPendingNotifications(10)
  
  if (!pending.success || !pending.data) {
    console.error('❌ Failed to get pending notifications:', pending.error)
    return
  }
  
  console.log(`📋 Found ${pending.data.length} pending notifications`)
  
  for (const notification of pending.data) {
    console.log(`🔄 Processing notification: ${notification.id}`)
    
    // Mark as processing
    const processingResult = await notificationQueueService.markNotificationProcessing(notification.id)
    
    if (!processingResult.success) {
      console.error('❌ Failed to mark as processing:', processingResult.error)
      continue
    }
    
    try {
      // Simulate sending notification (replace with actual Expo Push API call)
      console.log(`📤 Sending to ${notification.device_type} device: ${notification.push_token}`)
      console.log(`   Title: ${notification.title}`)
      console.log(`   Body: ${notification.body}`)
      console.log(`   Channel: ${notification.channel_type}`)
      console.log(`   Data:`, notification.data)
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // Simulate success (90% success rate)
      if (Math.random() > 0.1) {
        // Mark as sent (removes from queue)
        const sentResult = await notificationQueueService.markNotificationSent(notification.id)
        
        if (sentResult.success) {
          console.log('✅ Notification sent successfully')
        } else {
          console.error('❌ Failed to mark as sent:', sentResult.error)
        }
      } else {
        // Simulate failure
        throw new Error('Simulated network error')
      }
      
    } catch (error) {
      console.error('❌ Failed to send notification:', error)
      
      // Mark as failed (will retry or remove if max attempts reached)
      const failedResult = await notificationQueueService.markNotificationFailed(
        notification.id,
        error instanceof Error ? error.message : 'Unknown error'
      )
      
      if (!failedResult.success) {
        console.error('❌ Failed to mark as failed:', failedResult.error)
      }
    }
  }
}

// Example: Cleanup expired notifications
async function cleanupExpiredNotifications() {
  console.log('🧹 Cleaning up expired notifications...')
  
  const result = await notificationQueueService.cleanupExpiredNotifications()
  
  if (result.success) {
    console.log(`✅ Cleaned up ${result.data} expired notifications`)
  } else {
    console.error('❌ Failed to cleanup:', result.error)
  }
}

// Example: Get queue statistics
async function getQueueStatistics() {
  console.log('📊 Getting queue statistics...')
  
  const result = await notificationQueueService.getQueueStatistics()
  
  if (result.success && result.data) {
    console.log('📈 Queue Statistics:')
    console.log(`   Pending: ${result.data.total_pending}`)
    console.log(`   Processing: ${result.data.total_processing}`)
    console.log(`   Expired: ${result.data.total_expired}`)
    console.log(`   Oldest pending: ${result.data.oldest_pending}`)
    console.log(`   Newest pending: ${result.data.newest_pending}`)
  } else {
    console.error('❌ Failed to get statistics:', result.error)
  }
}

// Example: Queue notifications with different priorities and TTLs
async function queueVariousNotifications() {
  console.log('🎯 Queuing various types of notifications...')
  
  // High priority, short TTL - urgent notification
  await notificationQueueService.queueNotificationForUser('user-123', {
    title: 'Security Alert',
    body: 'Unusual login detected on your account',
    channelType: 'other',
    ttlMinutes: 15, // Expire quickly
    priority: 10, // Very high priority
    data: { type: 'security_alert', action: 'review_login' }
  })
  
  // Normal priority, medium TTL - recommendation
  await notificationQueueService.queueNotificationForUser('user-123', {
    title: 'Weekly Recommendations',
    body: 'Check out this week\'s personalized recommendations',
    channelType: 'recommendations',
    ttlMinutes: 1440, // 24 hours
    priority: 0,
    data: { type: 'weekly_recommendations', week: '2025-W03' }
  })
  
  // Low priority, long TTL - general update
  await notificationQueueService.queueNotificationForUser('user-123', {
    title: 'App Update Available',
    body: 'A new version of the app is available with exciting features',
    channelType: 'other',
    ttlMinutes: 10080, // 7 days
    priority: -1, // Lower priority
    data: { type: 'app_update', version: '2.1.0' }
  })
  
  console.log('✅ Various notifications queued')
}

// Main execution function
async function main() {
  console.log('🚀 Notification Queue System Example\n')
  
  try {
    // Queue some notifications
    await queueDeviceNotification()
    await queueUserNotification()
    await queueVariousNotifications()
    
    console.log('\n📊 Current queue status:')
    await getQueueStatistics()
    
    console.log('\n⚙️ Processing notifications:')
    await processPendingNotifications()
    
    console.log('\n🧹 Cleanup:')
    await cleanupExpiredNotifications()
    
    console.log('\n📊 Final queue status:')
    await getQueueStatistics()
    
  } catch (error) {
    console.error('💥 Example failed:', error)
  }
}

// Run the example if this file is executed directly
if (require.main === module) {
  main().then(() => {
    console.log('\n✅ Example completed')
    process.exit(0)
  }).catch((error) => {
    console.error('\n💥 Example failed:', error)
    process.exit(1)
  })
}

export {
  queueDeviceNotification,
  queueUserNotification,
  processPendingNotifications,
  cleanupExpiredNotifications,
  getQueueStatistics,
  queueVariousNotifications
}
