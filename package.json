{"name": "one-sub-mobile", "version": "0.0.1", "private": true, "main": "expo/AppEntry.js", "scripts": {"compile": "tsc --noEmit -p . --pretty", "format": "prettier --write \"app/**/*.{js,jsx,json,md,ts,tsx}\"", "lint": "eslint App.tsx app test --fix --ext .js,.ts,.tsx && npm run format", "patch": "patch-package", "test": "jest", "test:watch": "jest --watch", "test:maestro": "maestro test .maestro/FavoritePodcast.yaml", "adb": "adb reverse tcp:9090 tcp:9090 && adb reverse tcp:3000 tcp:3000 && adb reverse tcp:9001 tcp:9001 && adb reverse tcp:8081 tcp:8081", "postinstall": "patch-package", "bundle:visualize": "npx react-native-bundle-visualizer", "bundle:visualize:dev": "npx react-native-bundle-visualizer --dev", "build:ios:sim": "eas build --profile development --platform ios_bak --local", "build:ios:dev": "eas build --profile development:device --platform ios_bak --local", "build:ios:prod": "eas build --profile production --platform ios_bak --local", "build:android:sim": "eas build --profile development --platform android --local", "build:android:dev": "eas build --profile development:device --platform android --local", "build:android:prod": "eas build --profile production --platform android --local", "start": "npx expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "npx expo start --web", "bundle:web": "npx expo export --platform web", "serve:web": "npx server dist", "prebuild:clean": "npx expo prebuild --clean"}, "dependencies": {"@expo-google-fonts/space-grotesk": "^0.2.2", "@expo/metro-runtime": "~5.0.4", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-native-fontawesome": "^0.3.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-google-signin/google-signin": "^13.2.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@sentry/react-native": "~6.14.0", "@shopify/flash-list": "1.7.6", "@shopify/react-native-skia": "2.0.0-next.4", "@supabase/supabase-js": "^2.49.5-next.5", "aes-js": "^3.1.2", "apisauce": "3.0.1", "aws-amplify": "^6.14.4", "date-fns": "^2.30.0", "denque": "^2.1.0", "expo": "^53.0.11", "expo-auth-session": "^6.2.0", "expo-build-properties": "~0.14.6", "expo-dev-client": "~5.2.0", "expo-device": "^7.1.4", "expo-font": "~13.3.1", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-notifications": "^0.31.3", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-web-browser": "^14.1.6", "fast-fuzzy": "^1.12.0", "i18n-js": "^4.4.3", "mobx": "6.10.2", "mobx-react-lite": "4.0.5", "mobx-state-tree": "5.3.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "~0.79.3", "react-native-elements": "^3.4.3", "react-native-email-link": "^1.16.1", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-onboarding-swiper": "^1.3.0", "react-native-prompt-android": "^1.1.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "~5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-webview": "~13.13.5", "victory-native": "^41.12.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-transform-arrow-functions": "^7.0.0", "@babel/plugin-transform-shorthand-properties": "^7.0.0", "@babel/plugin-transform-template-literals": "^7.0.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "8.0.0-alpha.17", "@testing-library/react-native": "^12.5.2", "@types/i18n-js": "3.8.2", "@types/jest": "^29.2.1", "@types/react": "~19.0.0", "@types/react-native-onboarding-swiper": "^1.1.9", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "babel-jest": "^29.2.1", "eslint": "8.17.0", "eslint-config-prettier": "8.5.0", "eslint-config-standard": "17.0.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-n": "^15.0.0", "eslint-plugin-promise": "6.0.0", "eslint-plugin-react": "7.30.0", "eslint-plugin-react-native": "4.0.0", "eslint-plugin-reactotron": "^0.1.2", "jest": "^29.2.1", "jest-expo": "~53.0.7", "patch-package": "^8.0.0", "postinstall-prepare": "1.0.1", "prettier": "2.8.8", "react-native-vector-icons": "^10.2.0", "react-test-renderer": "19.0.0", "reactotron-core-client": "^2.9.7", "reactotron-mst": "^3.1.11", "reactotron-react-js": "^3.3.16", "reactotron-react-native": "^5.1.13", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "~5.8.3"}, "engines": {"node": ">=18"}, "prettier": {"printWidth": 100, "semi": false, "singleQuote": false, "trailingComma": "all"}, "eslintConfig": {"root": true, "parser": "@typescript-eslint/parser", "extends": ["plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-native/all", "standard", "prettier"], "plugins": ["@typescript-eslint", "react", "react-native", "reactotron"], "parserOptions": {"ecmaFeatures": {"jsx": true}}, "settings": {"react": {"pragma": "React", "version": "detect"}}, "globals": {"__DEV__": false, "jasmine": false, "beforeAll": false, "afterAll": false, "beforeEach": false, "afterEach": false, "test": false, "expect": false, "describe": false, "jest": false, "it": false}, "rules": {"@typescript-eslint/ban-ts-ignore": 0, "@typescript-eslint/ban-ts-comment": 0, "@typescript-eslint/explicit-function-return-type": 0, "@typescript-eslint/explicit-member-accessibility": 0, "@typescript-eslint/explicit-module-boundary-types": 0, "@typescript-eslint/indent": 0, "@typescript-eslint/member-delimiter-style": 0, "@typescript-eslint/no-empty-interface": 0, "@typescript-eslint/no-explicit-any": 0, "@typescript-eslint/no-object-literal-type-assertion": 0, "@typescript-eslint/no-var-requires": 0, "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "comma-dangle": 0, "multiline-ternary": 0, "no-undef": 0, "no-unused-vars": 0, "no-use-before-define": 0, "no-global-assign": 0, "quotes": 0, "react-native/no-raw-text": 0, "react/no-unescaped-entities": 0, "react/prop-types": 0, "space-before-function-paren": 0, "reactotron/no-tron-in-production": "error"}}}