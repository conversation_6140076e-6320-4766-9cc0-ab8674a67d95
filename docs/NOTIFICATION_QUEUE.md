# Notification Queue System

A comprehensive notification queuing mechanism for Supabase with configurable TTL (Time-To-Live) support. This system allows you to queue notifications for single devices or all devices for a given user, with automatic cleanup of expired notifications.

## Features

- ✅ Queue notifications for specific devices or all user devices
- ✅ Configurable TTL before notifications expire
- ✅ Priority-based queue processing
- ✅ Automatic retry mechanism with max attempts
- ✅ No persistent record keeping (notifications are removed after processing)
- ✅ Support for different notification channels (questions, recommendations, other)
- ✅ Comprehensive error handling and logging
- ✅ Queue statistics and monitoring

## Database Schema

### Tables

#### `notification_targets`
Stores device registration information for push notifications.

```sql
- id: UUID (Primary Key)
- user_id: UUID (Foreign Key to auth.users)
- push_token: TEXT (Device push token)
- device_type: TEXT (ios, android, web)
- device_info: JSONB (Device metadata)
- notification_preferences: JSONB (Channel preferences)
- is_active: BOOLEAN
- created_at, updated_at, last_notification_at: TIMESTAMPTZ
```

#### `notification_queue`
Temporary storage for queued notifications with TTL support.

```sql
- id: UUID (Primary Key)
- user_id: UUID (Foreign Key to auth.users)
- target_device_id: UUID (Foreign Key to notification_targets, NULL = all devices)
- title, body: TEXT (Notification content)
- data: JSONB (Additional payload)
- channel_type: TEXT (questions, recommendations, other)
- status: TEXT (pending, processing, sent, failed, expired)
- priority: INTEGER (Higher = more priority)
- max_attempts: INTEGER (Default: 3)
- attempt_count: INTEGER
- expires_at: TIMESTAMPTZ (TTL expiration)
- scheduled_for: TIMESTAMPTZ (When to send)
- created_at, updated_at, last_attempt_at: TIMESTAMPTZ
- error_message: TEXT
```

## Database Functions

### Queue Management

#### `queue_notification_for_device()`
Queue a notification for a specific device.

```sql
SELECT queue_notification_for_device(
  p_user_id := 'user-uuid',
  p_target_device_id := 'device-uuid',
  p_title := 'Notification Title',
  p_body := 'Notification Body',
  p_data := '{"key": "value"}',
  p_channel_type := 'questions',
  p_ttl_minutes := 60,
  p_priority := 1,
  p_scheduled_for := NOW()
);
```

#### `queue_notification_for_user()`
Queue a notification for all active devices of a user.

```sql
SELECT queue_notification_for_user(
  p_user_id := 'user-uuid',
  p_title := 'Notification Title',
  p_body := 'Notification Body',
  p_data := '{"key": "value"}',
  p_channel_type := 'recommendations',
  p_ttl_minutes := 120,
  p_priority := 0,
  p_scheduled_for := NOW()
);
```

### Processing Functions

#### `get_pending_notifications()`
Retrieve notifications ready to be sent.

```sql
SELECT * FROM get_pending_notifications(100);
```

#### `mark_notification_processing()`
Mark a notification as being processed.

```sql
SELECT mark_notification_processing('notification-uuid');
```

#### `mark_notification_sent()`
Mark notification as successfully sent (removes from queue).

```sql
SELECT mark_notification_sent('notification-uuid');
```

#### `mark_notification_failed()`
Mark notification as failed (retry or remove if max attempts reached).

```sql
SELECT mark_notification_failed('notification-uuid', 'Error message');
```

### Maintenance Functions

#### `cleanup_expired_notifications()`
Remove expired notifications from the queue.

```sql
SELECT cleanup_expired_notifications();
```

#### `get_queue_statistics()`
Get queue statistics for monitoring.

```sql
SELECT * FROM get_queue_statistics();
```

## TypeScript Service Usage

### Basic Usage

```typescript
import { notificationQueueService } from 'app/services/NotificationQueueService'

// Queue notification for specific device
const result = await notificationQueueService.queueNotificationForDevice(
  'user-id',
  'device-id',
  {
    title: 'Profile Question',
    body: 'Please complete your profile',
    channelType: 'questions',
    ttlMinutes: 60,
    priority: 1,
    data: { questionId: 'q123' }
  }
)

// Queue notification for all user devices
const userResult = await notificationQueueService.queueNotificationForUser(
  'user-id',
  {
    title: 'New Recommendation',
    body: 'Check out this product recommendation',
    channelType: 'recommendations',
    ttlMinutes: 120,
    priority: 0
  }
)
```

### Background Processing

```typescript
// Get pending notifications for processing
const pending = await notificationQueueService.getPendingNotifications(50)

if (pending.success && pending.data) {
  for (const notification of pending.data) {
    // Mark as processing
    await notificationQueueService.markNotificationProcessing(notification.id)
    
    try {
      // Send notification using Expo Push API
      await sendPushNotification(notification)
      
      // Mark as sent (removes from queue)
      await notificationQueueService.markNotificationSent(notification.id)
    } catch (error) {
      // Mark as failed (will retry or remove if max attempts reached)
      await notificationQueueService.markNotificationFailed(
        notification.id, 
        error.message
      )
    }
  }
}
```

### Maintenance

```typescript
// Cleanup expired notifications (run periodically)
const cleanupResult = await notificationQueueService.cleanupExpiredNotifications()
console.log(`Cleaned up ${cleanupResult.data} expired notifications`)

// Get queue statistics
const stats = await notificationQueueService.getQueueStatistics()
if (stats.success) {
  console.log('Queue Stats:', stats.data)
}
```

## Installation

1. **Run the migrations:**
   ```bash
   # Apply the database schema
   supabase db push
   
   # Or run migrations individually
   psql -f supabase/migrations/20250116000001_notification_queue.sql
   psql -f supabase/migrations/20250116000002_notification_queue_functions.sql
   ```

2. **Import the service:**
   ```typescript
   import { notificationQueueService } from 'app/services/NotificationQueueService'
   ```

## Configuration

### TTL Settings
- Default TTL: 60 minutes
- Configurable per notification
- Automatic cleanup of expired notifications

### Retry Logic
- Default max attempts: 3
- Automatic retry for failed notifications
- Exponential backoff can be implemented in processing logic

### Priority System
- Higher numbers = higher priority
- Default priority: 0
- Processed in priority order, then FIFO

## Monitoring

Use the `get_queue_statistics()` function to monitor:
- Total pending notifications
- Total processing notifications
- Total expired notifications
- Oldest/newest pending notification timestamps

## Security

- Row Level Security (RLS) enabled on all tables
- Users can only access their own notification data
- Service role has full access for background processing
- Proper function permissions granted

## Best Practices

1. **Regular Cleanup**: Run `cleanup_expired_notifications()` periodically
2. **Monitor Queue**: Check statistics regularly to prevent queue buildup
3. **Error Handling**: Always handle service response errors
4. **TTL Planning**: Set appropriate TTL based on notification urgency
5. **Priority Usage**: Use priority sparingly for truly urgent notifications
