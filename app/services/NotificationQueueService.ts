/**
 * NotificationQueueService - Service for managing notification queue with TTL support
 * Provides methods to queue notifications for single devices or all user devices
 */

import { supabase } from 'app/config/config.base'
import { ErrorType, ErrorExperience, reportSentryError } from 'app/utils/crashReporting'
import { NotificationChannelType } from 'app/types/notification.types'

// =====================================================
// Types
// =====================================================

export interface QueueNotificationRequest {
  title: string
  body: string
  data?: Record<string, any>
  channelType?: NotificationChannelType
  ttlMinutes?: number
  priority?: number
  scheduledFor?: Date
}

export interface QueuedNotification {
  id: string
  user_id: string
  target_device_id: string
  push_token: string
  device_type: string
  title: string
  body: string
  data: Record<string, any>
  channel_type: NotificationChannelType
  priority: number
  attempt_count: number
  max_attempts: number
}

export interface QueueStatistics {
  total_pending: number
  total_processing: number
  total_expired: number
  oldest_pending: string | null
  newest_pending: string | null
}

export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
}

// =====================================================
// NotificationQueueService Class
// =====================================================

export class NotificationQueueService {
  private static instance: NotificationQueueService

  public static getInstance(): NotificationQueueService {
    if (!NotificationQueueService.instance) {
      NotificationQueueService.instance = new NotificationQueueService()
    }
    return NotificationQueueService.instance
  }

  /**
   * Queue a notification for a specific device
   */
  public async queueNotificationForDevice(
    userId: string,
    targetDeviceId: string,
    request: QueueNotificationRequest
  ): Promise<ServiceResponse<string>> {
    try {
      const { data, error } = await supabase.rpc('queue_notification_for_device', {
        p_user_id: userId,
        p_target_device_id: targetDeviceId,
        p_title: request.title,
        p_body: request.body,
        p_data: request.data || {},
        p_channel_type: request.channelType || 'other',
        p_ttl_minutes: request.ttlMinutes || 60,
        p_priority: request.priority || 0,
        p_scheduled_for: request.scheduledFor?.toISOString() || new Date().toISOString()
      })

      if (error) throw error

      if (__DEV__) {
        console.tron.log('✅ Notification queued for device:', {
          notificationId: data,
          userId,
          targetDeviceId,
          title: request.title
        })
      }

      return { success: true, data }
    } catch (error) {
      const queueError = error instanceof Error ? error : new Error('Failed to queue notification for device')
      reportSentryError(queueError, ErrorType.HANDLED, ErrorExperience.Notifications)

      if (__DEV__) {
        console.tron.error('❌ Failed to queue notification for device:', error)
      }

      return {
        success: false,
        error: queueError.message
      }
    }
  }

  /**
   * Queue a notification for all devices of a user
   */
  public async queueNotificationForUser(
    userId: string,
    request: QueueNotificationRequest
  ): Promise<ServiceResponse<string[]>> {
    try {
      const { data, error } = await supabase.rpc('queue_notification_for_user', {
        p_user_id: userId,
        p_title: request.title,
        p_body: request.body,
        p_data: request.data || {},
        p_channel_type: request.channelType || 'other',
        p_ttl_minutes: request.ttlMinutes || 60,
        p_priority: request.priority || 0,
        p_scheduled_for: request.scheduledFor?.toISOString() || new Date().toISOString()
      })

      if (error) throw error

      if (__DEV__) {
        console.tron.log('✅ Notification queued for user:', {
          notificationIds: data,
          userId,
          title: request.title,
          deviceCount: data?.length || 0
        })
      }

      return { success: true, data: data || [] }
    } catch (error) {
      const queueError = error instanceof Error ? error : new Error('Failed to queue notification for user')
      reportSentryError(queueError, ErrorType.HANDLED, ErrorExperience.Notifications)

      if (__DEV__) {
        console.tron.error('❌ Failed to queue notification for user:', error)
      }

      return {
        success: false,
        error: queueError.message
      }
    }
  }

  /**
   * Get pending notifications ready to be sent (for background processing)
   */
  public async getPendingNotifications(limit: number = 100): Promise<ServiceResponse<QueuedNotification[]>> {
    try {
      const { data, error } = await supabase.rpc('get_pending_notifications', {
        p_limit: limit
      })

      if (error) throw error

      return { success: true, data: data || [] }
    } catch (error) {
      const fetchError = error instanceof Error ? error : new Error('Failed to get pending notifications')
      reportSentryError(fetchError, ErrorType.HANDLED, ErrorExperience.Notifications)

      if (__DEV__) {
        console.tron.error('❌ Failed to get pending notifications:', error)
      }

      return {
        success: false,
        error: fetchError.message
      }
    }
  }

  /**
   * Mark notification as being processed
   */
  public async markNotificationProcessing(notificationId: string): Promise<ServiceResponse<boolean>> {
    try {
      const { data, error } = await supabase.rpc('mark_notification_processing', {
        p_notification_id: notificationId
      })

      if (error) throw error

      return { success: true, data }
    } catch (error) {
      const markError = error instanceof Error ? error : new Error('Failed to mark notification as processing')
      reportSentryError(markError, ErrorType.HANDLED, ErrorExperience.Notifications)

      return {
        success: false,
        error: markError.message
      }
    }
  }

  /**
   * Mark notification as successfully sent (removes from queue)
   */
  public async markNotificationSent(notificationId: string): Promise<ServiceResponse<boolean>> {
    try {
      const { data, error } = await supabase.rpc('mark_notification_sent', {
        p_notification_id: notificationId
      })

      if (error) throw error

      return { success: true, data }
    } catch (error) {
      const markError = error instanceof Error ? error : new Error('Failed to mark notification as sent')
      reportSentryError(markError, ErrorType.HANDLED, ErrorExperience.Notifications)

      return {
        success: false,
        error: markError.message
      }
    }
  }

  /**
   * Mark notification as failed
   */
  public async markNotificationFailed(
    notificationId: string, 
    errorMessage?: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      const { data, error } = await supabase.rpc('mark_notification_failed', {
        p_notification_id: notificationId,
        p_error_message: errorMessage
      })

      if (error) throw error

      return { success: true, data }
    } catch (error) {
      const markError = error instanceof Error ? error : new Error('Failed to mark notification as failed')
      reportSentryError(markError, ErrorType.HANDLED, ErrorExperience.Notifications)

      return {
        success: false,
        error: markError.message
      }
    }
  }

  /**
   * Cleanup expired notifications
   */
  public async cleanupExpiredNotifications(): Promise<ServiceResponse<number>> {
    try {
      const { data, error } = await supabase.rpc('cleanup_expired_notifications')

      if (error) throw error

      if (__DEV__ && data > 0) {
        console.tron.log('🧹 Cleaned up expired notifications:', { count: data })
      }

      return { success: true, data }
    } catch (error) {
      const cleanupError = error instanceof Error ? error : new Error('Failed to cleanup expired notifications')
      reportSentryError(cleanupError, ErrorType.HANDLED, ErrorExperience.Notifications)

      return {
        success: false,
        error: cleanupError.message
      }
    }
  }

  /**
   * Get queue statistics
   */
  public async getQueueStatistics(): Promise<ServiceResponse<QueueStatistics>> {
    try {
      const { data, error } = await supabase.rpc('get_queue_statistics')

      if (error) throw error

      return { success: true, data: data?.[0] }
    } catch (error) {
      const statsError = error instanceof Error ? error : new Error('Failed to get queue statistics')
      reportSentryError(statsError, ErrorType.HANDLED, ErrorExperience.Notifications)

      return {
        success: false,
        error: statsError.message
      }
    }
  }
}

// Export singleton instance
export const notificationQueueService = NotificationQueueService.getInstance()
